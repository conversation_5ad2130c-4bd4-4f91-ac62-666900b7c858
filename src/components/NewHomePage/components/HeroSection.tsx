import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import { Button } from "@/components/UI/Button";
import Marquee from "react-fast-marquee";
import {
  DisplaySmall,
  Heading2XLarge,
  HeadingSmall,
  HeadingXLarge,
} from "@/components/UI/Typography";
import Image from "next/image";

export type HeroSectionProps = {
  pill: string;
  heading: string;
  subheading: string;
};
const HeroSection = () => {

  const images = [
    "https://cdn.oasr.in/oa-site/cms-uploads/media/icici_lombard_logo_b73c659d45.jpeg",
    "https://cdn.oasr.in/oa-site/cms-uploads/media/LOGO_care_health_insurance_2c0265102e.svg",
    "https://cdn.oasr.in/oa-site/cms-uploads/media/logo_hdfc_22e6758170.png",
    "https://cdn.oasr.in/oa-site/cms-uploads/media/thumbnail_Niva_health_insurance_logo_a32bd384cc.png",
    "https://cdn.oasr.in/oa-site/cms-uploads/media/thumbnail_ABHI_logo_d425a45a55.jpg",
    "https://cdn.oasr.in/oa-site/cms-uploads/media/bajaj_logo_67de220632.png",
    "https://cdn.oasr.in/oa-site/cms-uploads/media/icici_lombard_logo_b73c659d45.jpeg",
    "https://cdn.oasr.in/oa-site/cms-uploads/media/LOGO_care_health_insurance_2c0265102e.svg",
    "https://cdn.oasr.in/oa-site/cms-uploads/media/logo_hdfc_22e6758170.png",
    "https://cdn.oasr.in/oa-site/cms-uploads/media/thumbnail_Niva_health_insurance_logo_a32bd384cc.png",
    "https://cdn.oasr.in/oa-site/cms-uploads/media/thumbnail_ABHI_logo_d425a45a55.jpg",
    "https://cdn.oasr.in/oa-site/cms-uploads/media/bajaj_logo_67de220632.png",
  ]

  return (
    <div className="w-full bg-primary-100 md:mt-[-122px] md:pt-[122px] md:pb-[250px] font-poppins relative md:mb-28">
      <SectionContainerLarge className="flex mt-20 z-10">
        <div className="w-[50%]">
          <HeadingXLarge className="text-neutral-1100 font-medium mb-4">
            Think of us as your Insurance Buddy - Smart, Helpful, and<br></br>
            <span className="relative inline-block py-8 pr-20">
              <Image
                src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5a69-86f9-7292-a073-1e65e1ff0e90/brush-stroke.png"
                alt=""
                className="absolute inset-0 w-full h-full ml-[-30px] z-0"
                fill
              />
              <span className="relative text-white font-normal italic align-top">
                on your side
              </span>
            </span>
          </HeadingXLarge>
          <HeadingSmall className="font-medium text-neutral-1100 mb-6">
            We help you pick the right plan, not just any plan
          </HeadingSmall>

          <div className="flex gap-8">
            <div className="relative inline-block p-0.5 hover:cursor-pointer">
              <div
                style={{
                  filter: "url(#squiggly)",
                }}
                className="absolute inset-0 border-4 border-primary-400 rounded-xl z-10"
              />
              <Button
                variant="primary"
                className="px-6 py-6 text-base font-normal rounded-xl hover:scale-100"
              >
                Talk To A Human
              </Button>
            </div>

            <div className="relative inline-block p-0.5 hover:cursor-pointer">
              <div
                style={{
                  filter: "url(#squiggly)",
                }}
                className="absolute inset-0 border-4 border-primary-800 rounded-xl z-10"
              />
              <Button
                variant="secondary"
                className="px-6 py-6 text-base font-normal rounded-xl hover:scale-100 bg-white text-primary-800"
              >
                Get Your Plan
              </Button>
            </div>
          </div>
        </div>
        <div className="w-[50%]"></div>
      </SectionContainerLarge>
      <Image
        src={
          "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5a86-8dd1-77a5-98af-de86cea5414a/e9039401e80d51e74132a5592865b132e9724cb4.png"
        }
        alt=""
        width={1215}
        height={844}
        className="absolute bottom-[-130px] left-[260px] z-0"
      />

      <SectionContainerMedium className="absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-1/2 !mb-0 border border-primary-300 rounded-xl bg-white p-10">
        <HeadingSmall className="font-medium text-center text-primary-800 mb-9">
          Backed By India's Most Trusted Insurers
        </HeadingSmall>
        {/* auto carousal of images*/}
        <Marquee className="overflow-hidden" speed={30} gradient={false}>
          {images.map((src, index) => (
            <div key={index} className="relative w-20 h-10 bg-white mx-4">
              <Image
                src={src}
                alt={`Insurance partner ${index + 1}`}
                className="object-contain"
                fill
              />
            </div>
          ))}
        </Marquee>
      </SectionContainerMedium>
    </div>
  );
};

export default HeroSection;
