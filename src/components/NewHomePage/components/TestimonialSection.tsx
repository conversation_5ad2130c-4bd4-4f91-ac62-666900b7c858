import Pill from "@/components/globals/DSComponentsV0/Pill";
import SectionContainer from "@/components/globals/SectionContainer";
import { BodyMedium, HeadingLarge, HeadingXLarge } from "@/components/UI/Typography";
import { useEffect, useState } from "react";

const TestimonialSection = () => {
  const storyPoints = [
    "Helped 43,000+ families feel protected, not pressured.",
    "Guided first-time buyers through their very first claim — calmly.",
    "Supported parents getting their first health cover for kids.",
    "Sorted 15,000+ claims that would’ve otherwise been stressful.",
    "Answered real questions at 11 PM — because emergencies don’t wait.",
    "Saved users ₹2.3 Cr worth of unnecessary add-ons last year."
  ];

  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % storyPoints.length);
    }, 3000); // Change every 3 seconds

    return () => clearInterval(interval);
  }, [storyPoints.length]);

  // Function to get the style for each message based on its position
  const getMessageStyle = (index: number) => {
    const position = (index - currentIndex + storyPoints.length) % storyPoints.length;

    switch (position) {
      case 0: // Current message (3rd position) - white with green background
        return "opacity-100 text-white bg-green-600 transform translate-y-0 scale-100";
      case 1: // Next message (4th position) - lighter
        return "opacity-70 text-primary-700 bg-transparent transform translate-y-2 scale-95";
      case 2: // 5th position - even more lighter
        return "opacity-40 text-primary-600 bg-transparent transform translate-y-4 scale-90";
      case storyPoints.length - 2: // 2nd position - darker than first
        return "opacity-80 text-primary-800 bg-transparent transform -translate-y-2 scale-95";
      case storyPoints.length - 1: // 1st position - most transparent
        return "opacity-30 text-primary-600 bg-transparent transform -translate-y-4 scale-90";
      default: // Hidden messages
        return "opacity-0 text-primary-600 bg-transparent transform translate-y-8 scale-85";
    }
  };

  return (
    <div className="bg-primary-100 pt-16 pb-28">
      <SectionContainer>
        <Pill
          pill="Success Stories"
          border={false}
          textColor="text-secondary-400"
          bgColor="bg-white"
          className="mb-3"
        />
        <HeadingXLarge className="text-primary-800 text-center font-medium">
          Hear From People Like You
        </HeadingXLarge>
      </SectionContainer>

      <div className="flex">
        <div className="flex flex-col gap-4 flex-1 bg-yellow-100 pl-28">
          <HeadingLarge className="text-primary-800 font-semibold">
            Every Policy Has a Story
          </HeadingLarge>

          <div className="relative h-96 flex flex-col justify-center">
            {storyPoints.map((point, index) => {
              const position = (index - currentIndex + storyPoints.length) % storyPoints.length;
              const isVisible = position < 5; // Show only 5 messages at a time

              return (
                <div
                  key={index}
                  className={`absolute w-full transition-all duration-1000 ease-in-out ${
                    isVisible ? 'block' : 'hidden'
                  }`}
                  style={{
                    top: `${position * 60}px`, // Stack messages vertically
                  }}
                >
                  <BodyMedium
                    className={`px-4 py-3 rounded-lg transition-all duration-1000 ease-in-out ${getMessageStyle(index)}`}
                  >
                    {point}
                  </BodyMedium>
                </div>
              );
            })}
          </div>
        </div>
        <div className="flex-1 bg-yellow-300"></div>
      </div>
    </div>
  );
};

export default TestimonialSection;
