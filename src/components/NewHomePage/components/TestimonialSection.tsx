import Pill from "@/components/globals/DSComponentsV0/Pill";
import SectionContainer from "@/components/globals/SectionContainer";
import { BodyMedium, HeadingLarge, HeadingXLarge } from "@/components/UI/Typography";

const TestimonialSection = () => {
  const storyPoints = [
    "Helped 43,000+ families feel protected, not pressured.",
    "Guided first-time buyers through their very first claim — calmly.",
    "Supported parents getting their first health cover for kids.",
    "Sorted 15,000+ claims that would’ve otherwise been stressful.",
    "Answered real questions at 11 PM — because emergencies don’t wait.",
    "Saved users ₹2.3 Cr worth of unnecessary add-ons last year."
  ]
  return (
    <div className="bg-primary-100 pt-16 pb-28">
      <SectionContainer>
        <Pill
          pill="Success Stories"
          border={false}
          textColor="text-secondary-400"
          bgColor="bg-white"
          className="mb-3"
        />
        <HeadingXLarge className="text-primary-800 text-center font-medium">
          Hear From People Like You
        </HeadingXLarge>
      </SectionContainer>

      <div className="flex">
        <div className="flex flex-col gap-4 flex-1 bg-yellow-100 pl-28">
          <HeadingLarge className="text-primary-800 font-semibold">
            Every Policy Has a Story
          </HeadingLarge>

          <div>
            
          </div>
        </div>
        <div className="flex-1 bg-yellow-300"></div>
      </div>
    </div>
  );
};

export default TestimonialSection;
