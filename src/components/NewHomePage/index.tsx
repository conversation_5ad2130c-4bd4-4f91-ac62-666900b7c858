"use client";
import React from "react";
import WhyPeopleChoose from "@/components/NewHomePage/components/WhyPeopleChoose";
import homePageData from "@/components/NewHomePage/components/data";
import HowOneAssureWork from "@/components/NewHomePage/components/HowOneAssureWork";
import AskAnythingSection from "@/components/NewHomePage/components/AskAnythingSection";
import HeroSection from "@/components/NewHomePage/components/HeroSection";
import HandBorder from "@/components/NewHomePage/components/HandBorder";
// import AskAnythingSection from "./components/AskAnythingSection";
import CheckSection from "@/components/NewHomePage/components/AskAnythingSection";
import TalkToHuman from "@/components/NewHomePage/components/TalkToHuman";
import MeetOurTeam from "@/components/NewHomePage/components/MeetOurTeam";
import TestimonialSection from "@/components/NewHomePage/components/TestimonialSection";

const index = () => {
  return (
    <>
      <HandBorder />
      <HeroSection />
      <WhyPeopleChoose
        pill_Content={homePageData.whyToChooseUs.pill_Content}
        title={homePageData.whyToChooseUs.title}
        cards={homePageData.whyToChooseUs.cards}
      />
      <HowOneAssureWork
        pill={homePageData.howOneAssureWork.pill}
        heading={homePageData.howOneAssureWork.heading}
        steps={homePageData.howOneAssureWork.steps}
      />
      {/* <AskAnythingSection
        pill={homePageData.askAnythingSection.pill}
        heading={homePageData.askAnythingSection.heading}
        subheading={homePageData.askAnythingSection.subheading}
      /> */}
      <AskAnythingSection
          pill={homePageData.askAnythingSection.pill}
        heading={homePageData.askAnythingSection.heading}
        subheading={homePageData.askAnythingSection.subheading}
      
      />

      <TestimonialSection />

      <TalkToHuman />
      <MeetOurTeam />
    </>
  );
};

export default index;
